<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Streaming Chat UI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .chat-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 800px;
            padding: 30px;
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        h2 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2rem;
            font-weight: 600;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .input-container {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
            align-items: center;
        }

        #query {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        #query:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        #query::placeholder {
            color: #a0a0a0;
        }

        button {
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        button:active {
            transform: translateY(0);
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        #response {
            white-space: pre-wrap;
            border: 2px solid #e1e5e9;
            border-radius: 15px;
            padding: 20px;
            min-height: 200px;
            max-height: 400px;
            overflow-y: auto;
            background: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            line-height: 1.6;
            color: #333;
            position: relative;
            transition: all 0.3s ease;
        }

        #response:empty::before {
            content: "Your response will appear here...";
            color: #a0a0a0;
            font-style: italic;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 5px;
            margin-top: 10px;
            color: #667eea;
            font-weight: 500;
        }

        .typing-indicator.active {
            display: flex;
        }

        .typing-dots {
            display: flex;
            gap: 3px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #667eea;
            animation: typingDot 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) {
            animation-delay: -0.32s;
        }

        .typing-dot:nth-child(2) {
            animation-delay: -0.16s;
        }

        @keyframes typingDot {

            0%,
            80%,
            100% {
                transform: scale(0.8);
                opacity: 0.5;
            }

            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .error-message {
            color: #e74c3c;
            background: rgba(231, 76, 60, 0.1);
            border: 1px solid rgba(231, 76, 60, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            font-weight: 500;
        }

        /* Scrollbar styling */
        #response::-webkit-scrollbar {
            width: 8px;
        }

        #response::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 10px;
        }

        #response::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 10px;
        }

        #response::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a6fd8, #6a4190);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .chat-container {
                padding: 20px;
                margin: 10px;
            }

            h2 {
                font-size: 1.5rem;
                margin-bottom: 20px;
            }

            .input-container {
                flex-direction: column;
                gap: 10px;
            }

            #query,
            button {
                width: 100%;
                padding: 12px 20px;
            }

            #response {
                min-height: 150px;
                max-height: 300px;
            }
        }

        /* Animation for new content */
        .new-content {
            animation: fadeInContent 0.3s ease-out;
        }

        @keyframes fadeInContent {
            from {
                opacity: 0;
                transform: translateY(10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>

<body>
    <div class="chat-container">
        <h2>Ask a Question</h2>
        <div class="input-container">
            <input type="text" id="query" placeholder="Type your question..." />
            <button onclick="sendQuery()" id="sendBtn">Send</button>
        </div>
        <div id="response"></div>
        <div class="typing-indicator" id="typingIndicator">
            <span>AI is typing</span>
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>
    </div>

    <script>
        let isStreaming = false;

        // Add Enter key support for input
        document.getElementById("query").addEventListener("keypress", function (event) {
            if (event.key === "Enter" && !isStreaming) {
                sendQuery();
            }
        });

        async function sendQuery() {
            const queryInput = document.getElementById("query");
            const query = queryInput.value.trim();
            const responseDiv = document.getElementById("response");
            const sendBtn = document.getElementById("sendBtn");
            const typingIndicator = document.getElementById("typingIndicator");

            // Validation
            if (!query) {
                showError("Please enter a question before sending.");
                return;
            }

            if (isStreaming) {
                return; // Prevent multiple simultaneous requests
            }

            // UI state management
            isStreaming = true;
            responseDiv.textContent = "";
            responseDiv.classList.remove("error-message");
            sendBtn.disabled = true;
            sendBtn.textContent = "Sending...";
            typingIndicator.classList.add("active");

            const url = `http://localhost:3001/api/v1/?apikey=test_api_key_1751884336144_vp9gospvg&query=${encodeURIComponent(query)}&stream=true`;

            try {
                const res = await fetch(url, {
                    method: "GET",
                    headers: {
                        "Accept": "text/event-stream"
                    }
                });

                if (!res.ok) {
                    throw new Error(`HTTP error! status: ${res.status}`);
                }

                const reader = res.body.getReader();
                const decoder = new TextDecoder("utf-8");
                let buffer = '';
                let hasContent = false;

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    buffer += decoder.decode(value, { stream: true });

                    // Process all complete "data:" chunks
                    const lines = buffer.split('\n');
                    buffer = lines.pop(); // last incomplete line

                    for (const line of lines) {
                        if (line.startsWith('data:')) {
                            try {
                                const jsonStr = line.replace(/^data:\s*/, '');
                                const dataObj = JSON.parse(jsonStr);

                                if (dataObj.type === 'content' && dataObj.content) {
                                    if (!hasContent) {
                                        hasContent = true;
                                        typingIndicator.classList.remove("active");
                                    }

                                    // Add content with smooth animation
                                    const currentContent = responseDiv.textContent;
                                    responseDiv.textContent = currentContent + dataObj.content;

                                    // Auto-scroll to bottom
                                    responseDiv.scrollTop = responseDiv.scrollHeight;
                                }
                            } catch (err) {
                                console.error('Failed to parse JSON chunk:', line);
                            }
                        }
                    }
                }

                // Clear query input after successful response
                if (hasContent) {
                    queryInput.value = "";
                }

            } catch (err) {
                console.error('Stream error:', err);
                showError(`Error: ${err.message || 'Failed to fetch response. Please check your connection and try again.'}`);
            } finally {
                // Reset UI state
                isStreaming = false;
                sendBtn.disabled = false;
                sendBtn.textContent = "Send";
                typingIndicator.classList.remove("active");
                queryInput.focus(); // Return focus to input
            }
        }

        function showError(message) {
            const responseDiv = document.getElementById("response");
            responseDiv.textContent = message;
            responseDiv.classList.add("error-message");
        }

        // Focus input on page load
        window.addEventListener('load', function () {
            document.getElementById("query").focus();
        });
    </script>
</body>

</html>